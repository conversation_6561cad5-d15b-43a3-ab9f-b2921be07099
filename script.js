// API配置
const API_CONFIG = {
    url: 'https://services.slingshot.lego.com/api/v4/lego_historic_product_read/_search',
    apiKey: 'p0OKLXd8US1YsquudM1Ov9Ja7H91jhamak9EMrRB'
};

// 全局变量
let currentProductData = null;
let allImages = [];

// DOM元素
const elements = {
    productNumber: document.getElementById('productNumber'),
    searchBtn: document.getElementById('searchBtn'),
    loading: document.getElementById('loading'),
    resultSection: document.getElementById('resultSection'),
    errorSection: document.getElementById('errorSection'),
    errorMessage: document.getElementById('errorMessage'),
    retryBtn: document.getElementById('retryBtn'),
    downloadAllBtn: document.getElementById('downloadAllBtn'),
    downloadStatus: document.getElementById('downloadStatus')
};

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM加载完成，开始初始化...');

    // 检查所有必需的DOM元素是否存在
    const missingElements = [];
    for (const [key, element] of Object.entries(elements)) {
        if (!element) {
            missingElements.push(key);
        }
    }

    if (missingElements.length > 0) {
        console.error('缺少以下DOM元素:', missingElements);
        alert('页面初始化失败，缺少必要的元素: ' + missingElements.join(', '));
        return;
    }

    console.log('所有DOM元素检查通过，绑定事件...');
    bindEvents();
    console.log('初始化完成！');
});

// 绑定事件
function bindEvents() {
    console.log('绑定搜索按钮事件...');
    elements.searchBtn.addEventListener('click', function(e) {
        console.log('搜索按钮被点击！');
        e.preventDefault();
        handleSearch();
    });

    console.log('绑定输入框回车事件...');
    elements.productNumber.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            console.log('输入框回车键被按下！');
            e.preventDefault();
            handleSearch();
        }
    });

    console.log('绑定重试按钮事件...');
    elements.retryBtn.addEventListener('click', function(e) {
        console.log('重试按钮被点击！');
        e.preventDefault();
        handleSearch();
    });

    console.log('绑定下载按钮事件...');
    elements.downloadAllBtn.addEventListener('click', function(e) {
        console.log('下载按钮被点击！');
        e.preventDefault();
        downloadAllImages();
    });

    console.log('所有事件绑定完成！');

    // 检查URL参数，支持预填充搜索内容
    checkUrlParameters();
}

// 检查URL参数
function checkUrlParameters() {
    const urlParams = new URLSearchParams(window.location.search);
    const searchParam = urlParams.get('search');

    if (searchParam) {
        console.log('从URL参数获取搜索内容:', searchParam);
        elements.productNumber.value = searchParam;

        // 延迟执行搜索，确保页面完全加载
        setTimeout(() => {
            handleSearch();
        }, 500);
    }
}

// 处理搜索
async function handleSearch() {
    console.log('开始处理搜索请求...');

    const input = elements.productNumber.value.trim();
    console.log('输入内容:', input);

    if (!input) {
        console.log('输入为空，显示错误信息');
        showError('请输入套装编号');
        return;
    }

    // 解析输入，支持多个编号（空格分隔）
    const productNumbers = input.split(/\s+/).filter(num => num.length > 0);
    console.log('解析出的套装编号:', productNumbers);

    if (productNumbers.length === 0) {
        showError('请输入有效的套装编号');
        return;
    }

    console.log('显示加载状态...');
    showLoading();
    hideError();
    hideResult();

    try {
        if (productNumbers.length === 1) {
            // 单个套装搜索（保持原有逻辑）
            console.log('单个套装搜索...');
            const data = await searchProduct(productNumbers[0]);
            console.log('API返回数据:', data);

            if (data && data.hits && data.hits.hits && data.hits.hits.length > 0) {
                console.log('找到产品数据，开始显示信息...');
                currentProductData = data.hits.hits[0]._source;
                displayProductInfo(currentProductData);
                showResult();
                console.log('产品信息显示完成！');
            } else {
                console.log('未找到产品数据');
                showError('未找到该套装信息，请检查编号是否正确');
            }
        } else {
            // 多个套装搜索
            console.log(`多套装搜索，共${productNumbers.length}个套装...`);
            await handleMultipleSearch(productNumbers);
        }
    } catch (error) {
        console.error('搜索错误:', error);
        showError('查询失败，请检查网络连接或稍后重试');
    } finally {
        console.log('隐藏加载状态...');
        hideLoading();
    }
}

// 调用API搜索产品
async function searchProduct(productNumber) {
    const searchQuery = {
        "query": {
            "match": {
                "product_number": productNumber
            }
        }
    };

    console.log('开始API调用，优先使用本地代理...');

    // 直接使用本地代理，不尝试直接API调用（避免CORS问题）
    return await searchProductWithProxy(searchQuery);
}

// 处理多套装搜索
async function handleMultipleSearch(productNumbers) {
    console.log('开始多套装搜索...');

    // 更新加载状态显示进度
    updateLoadingProgress(0, productNumbers.length);

    const results = [];
    const errors = [];

    // 并发搜索所有套装（限制并发数量避免过载）
    const batchSize = 3; // 每批最多3个并发请求

    for (let i = 0; i < productNumbers.length; i += batchSize) {
        const batch = productNumbers.slice(i, i + batchSize);
        console.log(`处理批次 ${Math.floor(i/batchSize) + 1}:`, batch);

        const batchPromises = batch.map(async (productNumber, batchIndex) => {
            const globalIndex = i + batchIndex;
            try {
                console.log(`搜索套装 ${productNumber}...`);
                const data = await searchProduct(productNumber);

                updateLoadingProgress(globalIndex + 1, productNumbers.length);

                if (data && data.hits && data.hits.hits && data.hits.hits.length > 0) {
                    console.log(`套装 ${productNumber} 搜索成功`);
                    return {
                        productNumber,
                        success: true,
                        data: data.hits.hits[0]._source
                    };
                } else {
                    console.log(`套装 ${productNumber} 未找到`);
                    return {
                        productNumber,
                        success: false,
                        error: '未找到该套装'
                    };
                }
            } catch (error) {
                console.error(`套装 ${productNumber} 搜索失败:`, error);
                updateLoadingProgress(globalIndex + 1, productNumbers.length);
                return {
                    productNumber,
                    success: false,
                    error: error.message || '搜索失败'
                };
            }
        });

        const batchResults = await Promise.all(batchPromises);

        // 分类结果
        batchResults.forEach(result => {
            if (result.success) {
                results.push(result);
            } else {
                errors.push(result);
            }
        });

        // 批次间添加小延迟，避免API过载
        if (i + batchSize < productNumbers.length) {
            await new Promise(resolve => setTimeout(resolve, 500));
        }
    }

    console.log(`多套装搜索完成: 成功${results.length}个, 失败${errors.length}个`);

    // 显示结果
    displayMultipleResults(results, errors);
    showResult();
}

// 使用CORS代理的备用方法
async function searchProductWithProxy(searchQuery) {
    console.log('开始尝试代理方案...');

    // 尝试本地代理服务器
    try {
        console.log('尝试本地代理服务器...');
        const localProxyUrl = 'http://localhost:3000/api/search';

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时

        const response = await fetch(localProxyUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(searchQuery),
            signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (response.ok) {
            console.log('本地代理成功！获取到真实API数据');
            const data = await response.json();
            console.log('API响应数据:', data);
            return data;
        } else {
            const errorText = await response.text();
            console.error('本地代理返回错误:', response.status, errorText);
            throw new Error(`本地代理返回错误: ${response.status} - ${errorText}`);
        }
    } catch (localError) {
        console.error('本地代理失败:', localError.message);

        if (localError.name === 'AbortError') {
            throw new Error('请求超时：LEGO API响应时间过长，请稍后重试');
        }

        // 尝试公共CORS代理作为备用
        console.log('尝试公共CORS代理...');
        return await tryPublicProxy(searchQuery);
    }
}

// 尝试公共CORS代理
async function tryPublicProxy(searchQuery) {
    const publicProxies = [
        'https://cors-anywhere.herokuapp.com/',
        'https://api.allorigins.win/raw?url=',
        'https://corsproxy.io/?'
    ];

    for (const proxyUrl of publicProxies) {
        try {
            console.log(`尝试公共代理: ${proxyUrl}`);

            let requestUrl;
            let requestOptions;

            if (proxyUrl.includes('allorigins')) {
                requestUrl = `${proxyUrl}${encodeURIComponent(API_CONFIG.url)}`;
                requestOptions = {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `ApiKey ${API_CONFIG.apiKey}`
                        },
                        body: JSON.stringify(searchQuery)
                    })
                };
            } else {
                requestUrl = proxyUrl + API_CONFIG.url;
                requestOptions = {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `ApiKey ${API_CONFIG.apiKey}`,
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify(searchQuery)
                };
            }

            const response = await fetch(requestUrl, requestOptions);

            if (response.ok) {
                console.log(`公共代理成功: ${proxyUrl}`);
                const data = await response.json();
                console.log('公共代理API响应数据:', data);
                return data;
            } else {
                console.log(`公共代理失败: ${proxyUrl} - ${response.status}`);
            }
        } catch (error) {
            console.log(`公共代理错误: ${proxyUrl} - ${error.message}`);
        }
    }

    // 所有代理都失败了
    throw new Error('无法连接到LEGO API：所有代理服务器都不可用。请检查网络连接或稍后重试。');
}

// 显示产品信息
function displayProductInfo(product) {
    // 获取本地化信息
    const locale = getLocalizedInfo(product.locale);
    
    // 基本信息
    document.getElementById('productTitle').textContent = locale.display_title || locale.title || '未知套装';
    document.getElementById('productNumberDisplay').textContent = `编号: ${product.product_number}`;
    
    // 颗粒数量
    const pieceCount = product.product_versions && product.product_versions[0] 
        ? product.product_versions[0].piece_count 
        : '未知';
    document.getElementById('pieceCount').textContent = `颗粒数: ${pieceCount}`;
    
    // 日期信息
    const availability = product.availability || (product.product_versions && product.product_versions[0] && product.product_versions[0].availability);
    document.getElementById('launchDate').textContent = formatDate(availability && availability.launchDate) || '未知';
    document.getElementById('exitDate').textContent = formatDate(availability && availability.marketingExitDate) || '未知';
    
    // 描述信息
    document.getElementById('description').textContent = locale.description || '暂无介绍';
    document.getElementById('commDescription').textContent = locale.comm_description || '暂无推广介绍';
    
    // 详细介绍
    displayBulletPoints(locale.bullet_points);
    
    // 角色信息
    displayCharacters(locale.characters);
    
    // 亮点信息
    displayFeatures(locale.features);
    
    // 图片信息
    collectAndDisplayImages(product);
}

// 获取本地化信息（优先中文，后备英文）
function getLocalizedInfo(locale) {
    const zhCN = locale && locale['zh-cn'];
    const enUS = locale && locale['en-us'];

    return {
        display_title: (zhCN && zhCN.display_title) || (zhCN && zhCN.title) || (enUS && enUS.display_title) || (enUS && enUS.title),
        title: (zhCN && zhCN.title) || (enUS && enUS.title),
        description: (zhCN && zhCN.description) || (enUS && enUS.description),
        comm_description: (zhCN && zhCN.comm_description) || (enUS && enUS.comm_description),
        bullet_points: (zhCN && zhCN.bullet_points) || (enUS && enUS.bullet_points),
        characters: (zhCN && zhCN.characters) || (enUS && enUS.characters),
        features: (zhCN && zhCN.features) || (enUS && enUS.features),
        additional_data: (zhCN && zhCN.additional_data) || (enUS && enUS.additional_data)
    };
}

// 格式化日期
function formatDate(dateString) {
    if (!dateString) return null;
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN');
}

// 显示详细介绍
function displayBulletPoints(bulletPoints) {
    const container = document.getElementById('bulletPoints');
    if (bulletPoints) {
        const points = bulletPoints.split('•').filter(point => point.trim());
        container.innerHTML = points.map(point => 
            `<div class="list-item">${point.trim()}</div>`
        ).join('');
    } else {
        container.innerHTML = '<p>暂无详细介绍</p>';
    }
}

// 显示角色信息
function displayCharacters(characters) {
    const section = document.getElementById('charactersSection');
    const container = document.getElementById('characters');
    
    if (characters && characters.length > 0) {
        container.innerHTML = characters.map(character => 
            `<span class="character-tag">${character}</span>`
        ).join('');
        section.style.display = 'block';
    } else {
        section.style.display = 'none';
    }
}

// 显示亮点信息
function displayFeatures(features) {
    const section = document.getElementById('featuresSection');
    const container = document.getElementById('features');
    
    if (features && features.length > 0) {
        container.innerHTML = features.map(feature => 
            `<div class="list-item">${feature}</div>`
        ).join('');
        section.style.display = 'block';
    } else {
        section.style.display = 'none';
    }
}

// 收集并显示图片
function collectAndDisplayImages(product) {
    allImages = [];
    
    // 从assets收集图片
    if (product.assets) {
        product.assets.forEach(asset => {
            if (asset.assetFiles) {
                asset.assetFiles.forEach(file => {
                    if (file.url && file.type && file.type.includes('img')) {
                        allImages.push({
                            url: file.url,
                            filename: file.fileName || `image_${allImages.length + 1}.jpg`,
                            type: 'asset'
                        });
                    }
                });
            }
        });
    }
    
    // 从locale的additional_data收集图片
    const locale = getLocalizedInfo(product.locale);
    if (locale.additional_data) {
        const additionalData = locale.additional_data;
        
        // 主图片
        if (additionalData.primary_image_grownups && additionalData.primary_image_grownups.url) {
            allImages.push({
                url: additionalData.primary_image_grownups.url,
                filename: additionalData.primary_image_grownups.filename || 'primary_image.jpg',
                type: 'primary'
            });
        }
        
        // 盒子图片
        if (additionalData.box_image && additionalData.box_image.url) {
            allImages.push({
                url: additionalData.box_image.url,
                filename: additionalData.box_image.filename || 'box_image.png',
                type: 'box'
            });
        }
        
        // 附加图片
        if (additionalData.additional_images) {
            additionalData.additional_images.forEach((imgData, index) => {
                if (imgData.kid_image && imgData.kid_image.image && imgData.kid_image.image.url) {
                    allImages.push({
                        url: imgData.kid_image.image.url,
                        filename: imgData.kid_image.image.filename || `kid_image_${index + 1}.png`,
                        type: 'kid'
                    });
                }
                if (imgData.grown_up_image && imgData.grown_up_image.image && imgData.grown_up_image.image.url) {
                    allImages.push({
                        url: imgData.grown_up_image.image.url,
                        filename: imgData.grown_up_image.image.filename || `grown_up_image_${index + 1}.jpg`,
                        type: 'grown_up'
                    });
                }
            });
        }
    }
    
    displayImages();
}

// 显示图片
function displayImages() {
    const container = document.getElementById('imagesGrid');

    if (allImages.length === 0) {
        container.innerHTML = '<p>暂无图片</p>';
        return;
    }

    container.innerHTML = allImages.map((image, index) => `
        <div class="image-item">
            <img src="${image.url}" alt="套装图片 ${index + 1}" loading="lazy"
                 onerror="this.style.display='none'"
                 onclick="openImageModal('${image.url}', '套装图片 ${index + 1}', ${index})"
                 title="点击查看大图">
        </div>
    `).join('');
}

// 下载所有图片
async function downloadAllImages() {
    if (allImages.length === 0) {
        elements.downloadStatus.textContent = '没有可下载的图片';
        return;
    }
    
    elements.downloadAllBtn.disabled = true;
    elements.downloadStatus.textContent = '正在下载图片...';
    
    try {
        // 创建images文件夹（如果不存在）
        await createImagesFolder();
        
        let downloadedCount = 0;
        const total = allImages.length;
        
        for (let i = 0; i < allImages.length; i++) {
            const image = allImages[i];
            try {
                await downloadImage(image.url, image.filename);
                downloadedCount++;
                elements.downloadStatus.textContent = `已下载 ${downloadedCount}/${total} 张图片`;
            } catch (error) {
                console.error(`下载图片失败: ${image.filename}`, error);
            }
        }
        
        elements.downloadStatus.textContent = `下载完成！共下载 ${downloadedCount}/${total} 张图片`;
    } catch (error) {
        console.error('下载过程中出错:', error);
        elements.downloadStatus.textContent = '下载失败，请重试';
    } finally {
        elements.downloadAllBtn.disabled = false;
    }
}

// 创建images文件夹
async function createImagesFolder() {
    // 注意：在浏览器环境中，我们无法直接创建文件夹
    // 这里只是一个占位函数，实际的文件下载会使用浏览器的下载功能
    return Promise.resolve();
}

// 下载单个图片
async function downloadImage(url, filename) {
    try {
        const response = await fetch(url);
        const blob = await response.blob();
        
        // 创建下载链接
        const downloadUrl = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(downloadUrl);
        
        // 添加延迟避免浏览器阻止多个下载
        await new Promise(resolve => setTimeout(resolve, 500));
    } catch (error) {
        throw new Error(`下载失败: ${filename}`);
    }
}

// 显示/隐藏状态函数
function showLoading() {
    elements.loading.style.display = 'block';
}

function hideLoading() {
    elements.loading.style.display = 'none';
}

function showResult() {
    elements.resultSection.style.display = 'block';
}

function hideResult() {
    elements.resultSection.style.display = 'none';
}

function showError(message) {
    elements.errorMessage.textContent = message;
    elements.errorSection.style.display = 'block';
}

function hideError() {
    elements.errorSection.style.display = 'none';
}

// 更新加载进度
function updateLoadingProgress(current, total) {
    const loadingElement = elements.loading;
    const progressText = loadingElement.querySelector('p');

    if (total > 1) {
        progressText.textContent = `正在查询中... (${current}/${total})`;

        // 添加进度条
        let progressBar = loadingElement.querySelector('.progress-bar');
        if (!progressBar) {
            progressBar = document.createElement('div');
            progressBar.className = 'progress-bar';
            progressBar.innerHTML = '<div class="progress-fill"></div>';
            loadingElement.appendChild(progressBar);
        }

        const progressFill = progressBar.querySelector('.progress-fill');
        const percentage = (current / total) * 100;
        progressFill.style.width = `${percentage}%`;
    } else {
        progressText.textContent = '正在查询中...';
    }
}

// 显示多个套装的结果
function displayMultipleResults(results, errors) {
    const resultSection = elements.resultSection;

    // 清空现有内容
    resultSection.innerHTML = '';

    // 创建多套装结果容器
    const multiResultContainer = document.createElement('div');
    multiResultContainer.className = 'multi-result-container';

    // 添加标题和统计
    const header = document.createElement('div');
    header.className = 'multi-result-header';
    header.innerHTML = `
        <h2>🔍 多套装查询结果</h2>
        <div class="result-stats">
            <span class="success-count">✅ 成功: ${results.length}</span>
            <span class="error-count">❌ 失败: ${errors.length}</span>
            <span class="total-count">📊 总计: ${results.length + errors.length}</span>
        </div>
    `;
    multiResultContainer.appendChild(header);

    // 显示成功的结果
    if (results.length > 0) {
        const successSection = document.createElement('div');
        successSection.className = 'success-results';
        successSection.innerHTML = '<h3>✅ 查询成功的套装</h3>';

        const resultsGrid = document.createElement('div');
        resultsGrid.className = 'results-grid';

        results.forEach(result => {
            const card = createProductCard(result.data, result.productNumber);
            resultsGrid.appendChild(card);
        });

        successSection.appendChild(resultsGrid);
        multiResultContainer.appendChild(successSection);
    }

    // 显示失败的结果
    if (errors.length > 0) {
        const errorSection = document.createElement('div');
        errorSection.className = 'error-results';
        errorSection.innerHTML = '<h3>❌ 查询失败的套装</h3>';

        const errorList = document.createElement('div');
        errorList.className = 'error-list';

        errors.forEach(error => {
            const errorItem = document.createElement('div');
            errorItem.className = 'error-item';
            errorItem.innerHTML = `
                <span class="error-number">${error.productNumber}</span>
                <span class="error-message">${error.error}</span>
            `;
            errorList.appendChild(errorItem);
        });

        errorSection.appendChild(errorList);
        multiResultContainer.appendChild(errorSection);
    }

    resultSection.appendChild(multiResultContainer);
}

// 恢复单套装显示布局
function restoreSingleProductLayout(hasMultiResults = false) {
    const resultSection = elements.resultSection;

    // 清空现有内容
    resultSection.innerHTML = '';

    // 创建返回按钮（如果有多套装结果）
    const backButtonHTML = hasMultiResults ? `
        <div class="back-to-list">
            <button class="back-button" onclick="returnToMultiResults()">
                ← 返回套装列表
            </button>
        </div>
    ` : '';

    // 重新创建单套装显示结构
    const productInfoHTML = `
        ${backButtonHTML}
        <div class="product-info">
            <div class="product-header">
                <h2 id="productTitle"></h2>
                <div class="product-meta">
                    <span class="product-number" id="productNumberDisplay"></span>
                    <span class="piece-count" id="pieceCount"></span>
                </div>
            </div>

            <div class="product-details">
                <div class="info-grid">
                    <div class="info-item">
                        <h3>📅 上市日期</h3>
                        <p id="launchDate"></p>
                    </div>
                    <div class="info-item">
                        <h3>📅 下架日期</h3>
                        <p id="exitDate"></p>
                    </div>
                </div>

                <div class="description-section">
                    <div class="info-item">
                        <h3>📝 套装介绍</h3>
                        <p id="description"></p>
                    </div>

                    <div class="info-item">
                        <h3>🎯 推广介绍</h3>
                        <p id="commDescription"></p>
                    </div>

                    <div class="info-item">
                        <h3>📋 详细介绍</h3>
                        <div id="bulletPoints"></div>
                    </div>

                    <div class="info-item" id="charactersSection" style="display: none;">
                        <h3>👥 角色介绍</h3>
                        <div id="characters"></div>
                    </div>

                    <div class="info-item" id="featuresSection" style="display: none;">
                        <h3>⭐ 亮点介绍</h3>
                        <div id="features"></div>
                    </div>
                </div>
            </div>

            <div class="images-section">
                <h3>🖼️ 套装图片</h3>
                <div class="images-grid" id="imagesGrid">
                    <!-- 图片将在这里动态加载 -->
                </div>
                <div class="download-section">
                    <button id="downloadAllBtn" class="download-btn">📥 下载所有图片</button>
                    <div id="downloadStatus"></div>
                </div>
            </div>
        </div>
    `;

    resultSection.innerHTML = productInfoHTML;

    // 重新绑定下载按钮事件
    const downloadBtn = document.getElementById('downloadAllBtn');
    if (downloadBtn) {
        downloadBtn.addEventListener('click', downloadAllImages);
    }

    console.log('单套装显示布局已恢复');
}

// 返回多套装查询结果
function returnToMultiResults() {
    if (multiSearchResults) {
        console.log('返回多套装查询结果');

        // 恢复多套装结果内容
        elements.resultSection.innerHTML = multiSearchResults.content;

        // 恢复输入框内容
        elements.productNumber.value = multiSearchResults.inputValue;

        // 重新绑定多套装结果中的事件
        rebindMultiResultEvents();

        // 滚动到结果区域
        elements.resultSection.scrollIntoView({ behavior: 'smooth' });

        console.log('多套装结果已恢复');
    } else {
        console.log('没有保存的多套装结果');
    }
}

// 重新绑定多套装结果中的事件
function rebindMultiResultEvents() {
    // 重新绑定所有"查看详情"按钮
    const detailButtons = document.querySelectorAll('[onclick^="viewProductDetails"]');
    detailButtons.forEach(button => {
        const onclick = button.getAttribute('onclick');
        const productNumber = onclick.match(/viewProductDetails\('([^']+)'\)/)[1];
        button.onclick = () => viewProductDetails(productNumber);
    });

    // 重新绑定所有"查看图片"按钮
    const imageButtons = document.querySelectorAll('[onclick^="viewProductImages"]');
    imageButtons.forEach(button => {
        const onclick = button.getAttribute('onclick');
        const productNumber = onclick.match(/viewProductImages\('([^']+)'\)/)[1];
        button.onclick = () => viewProductImages(productNumber);
    });
}

// 创建产品卡片
function createProductCard(productData, productNumber) {
    const locale = getLocalizedInfo(productData.locale);

    // 获取基本信息
    const title = locale.display_title || locale.title || `套装 ${productNumber}`;
    const pieceCount = productData.product_versions && productData.product_versions[0]
        ? productData.product_versions[0].piece_count
        : '未知';

    // 获取日期信息
    const availability = productData.availability ||
        (productData.product_versions && productData.product_versions[0] && productData.product_versions[0].availability);
    const launchDate = formatDate(availability && availability.launchDate) || '未知';

    // 获取主图片
    let mainImageUrl = '';
    if (productData.assets && productData.assets[0] && productData.assets[0].assetFiles && productData.assets[0].assetFiles[0]) {
        mainImageUrl = productData.assets[0].assetFiles[0].url;
    } else if (locale.additional_data && locale.additional_data.primary_image_grownups) {
        mainImageUrl = locale.additional_data.primary_image_grownups.url;
    }

    // 创建卡片
    const card = document.createElement('div');
    card.className = 'product-card';
    card.innerHTML = `
        <div class="card-header">
            <h4 class="card-title">${title}</h4>
            <span class="card-number">#${productNumber}</span>
        </div>

        ${mainImageUrl ? `
            <div class="card-image">
                <img src="${mainImageUrl}" alt="${title}" loading="lazy"
                     onerror="this.parentElement.style.display='none'"
                     onclick="openCardImageModal('${mainImageUrl}', '${title}', '${productNumber}')"
                     title="点击查看大图">
            </div>
        ` : ''}

        <div class="card-info">
            <div class="info-row">
                <span class="info-label">🧱 颗粒数:</span>
                <span class="info-value">${pieceCount}</span>
            </div>
            <div class="info-row">
                <span class="info-label">📅 上市日期:</span>
                <span class="info-value">${launchDate}</span>
            </div>
            ${locale.description ? `
                <div class="card-description">
                    ${locale.description.length > 100 ?
                        locale.description.substring(0, 100) + '...' :
                        locale.description}
                </div>
            ` : ''}
        </div>

        <div class="card-actions">
            <button class="btn-view-details" onclick="viewProductDetails('${productNumber}')">
                查看详情
            </button>
            <button class="btn-view-images" onclick="viewProductImages('${productNumber}')">
                查看图片
            </button>
        </div>
    `;

    return card;
}

// 存储多套装查询结果，用于返回列表
let multiSearchResults = null;

// 查看产品详情
async function viewProductDetails(productNumber) {
    console.log('查看产品详情:', productNumber);

    try {
        // 显示加载状态
        showLoading();
        hideError();

        // 保存当前的多套装结果（如果存在）
        const currentContent = elements.resultSection.innerHTML;
        const isMultiResult = currentContent.includes('multi-result-container');
        if (isMultiResult) {
            multiSearchResults = {
                content: currentContent,
                inputValue: elements.productNumber.value
            };
            console.log('保存了多套装查询结果');
        }

        // 在输入框中设置单个编号
        elements.productNumber.value = productNumber;

        // 直接调用单套装搜索，避免多套装逻辑
        console.log('开始单套装详情查询...');
        const data = await searchProduct(productNumber);
        console.log('详情查询API返回数据:', data);

        if (data && data.hits && data.hits.hits && data.hits.hits.length > 0) {
            console.log('找到产品数据，开始显示详情信息...');
            currentProductData = data.hits.hits[0]._source;

            // 确保结果区域恢复到单套装显示结构
            restoreSingleProductLayout(multiSearchResults !== null);

            displayProductInfo(currentProductData);
            showResult();

            // 滚动到结果区域
            elements.resultSection.scrollIntoView({ behavior: 'smooth' });
            console.log('产品详情显示完成！');
        } else {
            console.log('未找到产品数据');
            showError(`未找到套装 ${productNumber} 的详细信息`);
        }
    } catch (error) {
        console.error('查看详情错误:', error);
        showError(`查看套装 ${productNumber} 详情失败，请稍后重试`);
    } finally {
        hideLoading();
    }
}

// 查看产品图片
async function viewProductImages(productNumber) {
    console.log('查看产品图片:', productNumber);

    try {
        // 显示加载状态
        showLoading();
        hideError();

        // 获取产品数据
        console.log('获取产品图片数据...');
        const data = await searchProduct(productNumber);

        if (data && data.hits && data.hits.hits && data.hits.hits.length > 0) {
            const productData = data.hits.hits[0]._source;

            // 提取所有图片
            const images = [];
            if (productData.assets) {
                productData.assets.forEach(asset => {
                    if (asset.assetFiles) {
                        asset.assetFiles.forEach(file => {
                            if (file.url && file.url.includes('.jpg')) {
                                images.push({
                                    url: file.url,
                                    type: asset.type || '产品图片'
                                });
                            }
                        });
                    }
                });
            }

            if (images.length > 0) {
                // 创建图片查看器
                createImageViewer(productNumber, images);
            } else {
                showError(`套装 ${productNumber} 暂无可用图片`);
            }
        } else {
            showError(`未找到套装 ${productNumber} 的信息`);
        }
    } catch (error) {
        console.error('查看图片错误:', error);
        showError(`获取套装 ${productNumber} 图片失败，请稍后重试`);
    } finally {
        hideLoading();
    }
}

// 创建图片查看器
function createImageViewer(productNumber, images) {
    // 创建模态框
    const modal = document.createElement('div');
    modal.className = 'image-viewer-modal';
    modal.innerHTML = `
        <div class="image-viewer-content">
            <div class="image-viewer-header">
                <h3>套装 ${productNumber} 图片集 (${images.length}张)</h3>
                <button class="close-viewer" onclick="closeImageViewer()">&times;</button>
            </div>
            <div class="image-viewer-grid">
                ${images.map((img) => `
                    <div class="image-viewer-item">
                        <img src="${img.url}" alt="${img.type}" loading="lazy"
                             onclick="openFullImage('${img.url}')">
                        <p class="image-type">${img.type}</p>
                    </div>
                `).join('')}
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // 添加样式
    if (!document.getElementById('image-viewer-styles')) {
        const styles = document.createElement('style');
        styles.id = 'image-viewer-styles';
        styles.textContent = `
            .image-viewer-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                z-index: 1000;
                display: flex;
                align-items: center;
                justify-content: center;
                animation: fadeIn 0.3s ease;
            }

            .image-viewer-content {
                background: white;
                border-radius: 12px;
                max-width: 90%;
                max-height: 90%;
                overflow: auto;
                padding: 20px;
            }

            .image-viewer-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 20px;
                border-bottom: 2px solid #f0f0f0;
                padding-bottom: 15px;
            }

            .close-viewer {
                background: #dc3545;
                color: white;
                border: none;
                border-radius: 50%;
                width: 40px;
                height: 40px;
                font-size: 24px;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .image-viewer-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
                gap: 15px;
            }

            .image-viewer-item {
                text-align: center;
            }

            .image-viewer-item img {
                width: 100%;
                height: auto;
                min-height: 120px;
                max-height: 180px;
                object-fit: contain;
                background: white;
                border-radius: 8px;
                cursor: pointer;
                transition: transform 0.3s;
                border: 1px solid #e9ecef;
            }

            .image-viewer-item img:hover {
                transform: scale(1.02);
            }

            .image-type {
                margin: 8px 0 0 0;
                font-size: 0.9rem;
                color: #666;
            }

            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }
        `;
        document.head.appendChild(styles);
    }
}

// 关闭图片查看器
function closeImageViewer() {
    const modal = document.querySelector('.image-viewer-modal');
    if (modal) {
        modal.remove();
    }
}

// 打开全尺寸图片
function openFullImage(url) {
    window.open(url, '_blank');
}

// 打开图片模态框（用于详情页面的图片）
function openImageModal(imageUrl, imageTitle, imageIndex) {
    // 创建模态框
    const modal = document.createElement('div');
    modal.className = 'detail-image-modal';
    modal.innerHTML = `
        <div class="detail-image-content">
            <div class="detail-image-header">
                <h3>${imageTitle}</h3>
                <div class="image-nav">
                    <button class="nav-btn prev-btn" onclick="navigateImage(${imageIndex - 1})"
                            ${imageIndex === 0 ? 'disabled' : ''}>← 上一张</button>
                    <span class="image-counter">${imageIndex + 1} / ${allImages.length}</span>
                    <button class="nav-btn next-btn" onclick="navigateImage(${imageIndex + 1})"
                            ${imageIndex === allImages.length - 1 ? 'disabled' : ''}>下一张 →</button>
                </div>
                <button class="close-modal" onclick="closeImageModal()">&times;</button>
            </div>
            <div class="detail-image-body">
                <img src="${imageUrl}" alt="${imageTitle}" id="modalImage">
            </div>
            <div class="detail-image-footer">
                <button class="download-single-btn" onclick="downloadSingleImage('${imageUrl}', '${imageTitle}')">
                    📥 下载此图片
                </button>
                <button class="open-new-tab-btn" onclick="openFullImage('${imageUrl}')">
                    🔗 在新标签页打开
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // 添加样式（如果还没有）
    if (!document.getElementById('detail-image-modal-styles')) {
        const styles = document.createElement('style');
        styles.id = 'detail-image-modal-styles';
        styles.textContent = `
            .detail-image-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.9);
                z-index: 2000;
                display: flex;
                align-items: center;
                justify-content: center;
                animation: fadeIn 0.3s ease;
            }

            .detail-image-content {
                background: white;
                border-radius: 12px;
                max-width: 90%;
                max-height: 90%;
                overflow: hidden;
                display: flex;
                flex-direction: column;
            }

            .detail-image-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 15px 20px;
                border-bottom: 2px solid #f0f0f0;
                background: #f8f9fa;
            }

            .detail-image-header h3 {
                margin: 0;
                color: #333;
            }

            .image-nav {
                display: flex;
                align-items: center;
                gap: 15px;
            }

            .nav-btn {
                padding: 8px 16px;
                border: none;
                border-radius: 6px;
                background: #667eea;
                color: white;
                cursor: pointer;
                transition: background 0.3s;
            }

            .nav-btn:hover:not(:disabled) {
                background: #5a6fd8;
            }

            .nav-btn:disabled {
                background: #ccc;
                cursor: not-allowed;
            }

            .image-counter {
                color: #666;
                font-weight: bold;
            }

            .close-modal {
                background: #dc3545;
                color: white;
                border: none;
                border-radius: 50%;
                width: 40px;
                height: 40px;
                font-size: 24px;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .detail-image-body {
                flex: 1;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 20px;
                background: white;
            }

            .detail-image-body img {
                max-width: 100%;
                max-height: 70vh;
                object-fit: contain;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            }

            .detail-image-footer {
                padding: 15px 20px;
                background: #f8f9fa;
                border-top: 1px solid #e9ecef;
                display: flex;
                gap: 10px;
                justify-content: center;
            }

            .download-single-btn, .open-new-tab-btn {
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
                cursor: pointer;
                font-size: 0.9rem;
                transition: background 0.3s;
            }

            .download-single-btn {
                background: #28a745;
                color: white;
            }

            .download-single-btn:hover {
                background: #218838;
            }

            .open-new-tab-btn {
                background: #17a2b8;
                color: white;
            }

            .open-new-tab-btn:hover {
                background: #138496;
            }

            @media (max-width: 768px) {
                .detail-image-header {
                    flex-direction: column;
                    gap: 10px;
                    text-align: center;
                }

                .image-nav {
                    flex-direction: column;
                    gap: 8px;
                }

                .detail-image-footer {
                    flex-direction: column;
                }
            }
        `;
        document.head.appendChild(styles);
    }

    // 存储当前图片索引
    window.currentImageIndex = imageIndex;
}

// 导航到指定图片
function navigateImage(newIndex) {
    if (newIndex < 0 || newIndex >= allImages.length) return;

    const modal = document.querySelector('.detail-image-modal');
    if (!modal) return;

    const image = allImages[newIndex];
    const modalImage = document.getElementById('modalImage');
    const imageTitle = `套装图片 ${newIndex + 1}`;

    // 更新图片
    modalImage.src = image.url;
    modalImage.alt = imageTitle;

    // 更新标题
    modal.querySelector('.detail-image-header h3').textContent = imageTitle;

    // 更新计数器
    modal.querySelector('.image-counter').textContent = `${newIndex + 1} / ${allImages.length}`;

    // 更新导航按钮
    const prevBtn = modal.querySelector('.prev-btn');
    const nextBtn = modal.querySelector('.next-btn');

    prevBtn.disabled = newIndex === 0;
    nextBtn.disabled = newIndex === allImages.length - 1;

    prevBtn.onclick = () => navigateImage(newIndex - 1);
    nextBtn.onclick = () => navigateImage(newIndex + 1);

    // 更新下载按钮
    const downloadBtn = modal.querySelector('.download-single-btn');
    const openBtn = modal.querySelector('.open-new-tab-btn');

    downloadBtn.onclick = () => downloadSingleImage(image.url, imageTitle);
    openBtn.onclick = () => openFullImage(image.url);

    window.currentImageIndex = newIndex;
}

// 关闭图片模态框
function closeImageModal() {
    const modal = document.querySelector('.detail-image-modal');
    if (modal) {
        modal.remove();
    }
}

// 下载单张图片
async function downloadSingleImage(url, title) {
    try {
        const response = await fetch(url);
        const blob = await response.blob();

        const downloadUrl = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = `${title}.jpg`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
        console.error('下载图片失败:', error);
        alert('下载失败，请稍后重试');
    }
}

// 打开卡片图片模态框（用于多套装查询卡片的图片）
function openCardImageModal(imageUrl, title, productNumber) {
    // 创建简化的图片模态框
    const modal = document.createElement('div');
    modal.className = 'card-image-modal';
    modal.innerHTML = `
        <div class="card-image-content">
            <div class="card-image-header">
                <h3>${title} (#${productNumber})</h3>
                <button class="close-modal" onclick="closeCardImageModal()">&times;</button>
            </div>
            <div class="card-image-body">
                <img src="${imageUrl}" alt="${title}" id="cardModalImage">
            </div>
            <div class="card-image-footer">
                <button class="download-single-btn" onclick="downloadSingleImage('${imageUrl}', '${title}')">
                    📥 下载图片
                </button>
                <button class="open-new-tab-btn" onclick="openFullImage('${imageUrl}')">
                    🔗 在新标签页打开
                </button>
                <button class="view-details-btn" onclick="closeCardImageModal(); viewProductDetails('${productNumber}')">
                    👁️ 查看套装详情
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // 添加样式（如果还没有）
    if (!document.getElementById('card-image-modal-styles')) {
        const styles = document.createElement('style');
        styles.id = 'card-image-modal-styles';
        styles.textContent = `
            .card-image-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.9);
                z-index: 2000;
                display: flex;
                align-items: center;
                justify-content: center;
                animation: fadeIn 0.3s ease;
            }

            .card-image-content {
                background: white;
                border-radius: 12px;
                max-width: 80%;
                max-height: 80%;
                overflow: hidden;
                display: flex;
                flex-direction: column;
            }

            .card-image-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 15px 20px;
                border-bottom: 2px solid #f0f0f0;
                background: #f8f9fa;
            }

            .card-image-header h3 {
                margin: 0;
                color: #333;
                font-size: 1.2rem;
            }

            .card-image-body {
                flex: 1;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 20px;
                background: white;
                min-height: 300px;
            }

            .card-image-body img {
                max-width: 100%;
                max-height: 60vh;
                object-fit: contain;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            }

            .card-image-footer {
                padding: 15px 20px;
                background: #f8f9fa;
                border-top: 1px solid #e9ecef;
                display: flex;
                gap: 10px;
                justify-content: center;
                flex-wrap: wrap;
            }

            .view-details-btn {
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
                cursor: pointer;
                font-size: 0.9rem;
                transition: background 0.3s;
                background: #667eea;
                color: white;
            }

            .view-details-btn:hover {
                background: #5a6fd8;
            }

            @media (max-width: 768px) {
                .card-image-content {
                    max-width: 95%;
                    max-height: 90%;
                }

                .card-image-header {
                    flex-direction: column;
                    gap: 10px;
                    text-align: center;
                }

                .card-image-footer {
                    flex-direction: column;
                }
            }
        `;
        document.head.appendChild(styles);
    }
}

// 关闭卡片图片模态框
function closeCardImageModal() {
    const modal = document.querySelector('.card-image-modal');
    if (modal) {
        modal.remove();
    }
}

// 显示演示数据提示
function showDemoDataNotice() {
    // 在页面顶部添加一个提示条
    let notice = document.getElementById('demoNotice');
    if (!notice) {
        notice = document.createElement('div');
        notice.id = 'demoNotice';
        notice.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: #fff3cd;
            color: #856404;
            padding: 10px;
            text-align: center;
            border-bottom: 1px solid #ffeaa7;
            z-index: 1000;
            font-size: 14px;
        `;
        notice.innerHTML = `
            <strong>📋 演示模式</strong> - 当前显示的是演示数据，因为无法连接到LEGO API服务器
            <button onclick="this.parentElement.style.display='none'" style="margin-left: 10px; background: none; border: none; color: #856404; cursor: pointer;">✕</button>
        `;
        document.body.insertBefore(notice, document.body.firstChild);

        // 调整页面内容的上边距
        document.body.style.paddingTop = '50px';
    }
}

// 获取模拟数据（用于演示）
function getMockData(productNumber) {
    console.log(`生成套装 ${productNumber} 的模拟数据`);

    // 如果查询的是21326，返回你提供的示例数据
    if (productNumber === '21326') {
        return {
            "_isMockData": true,
            "hits": {
                "hits": [{
                    "_source": {
                        "product_number": "21326",
                        "product_versions": [{
                            "piece_count": 1265,
                            "availability": {
                                "marketingExitDate": "2023-12-31T00:00:00",
                                "launchDate": "2021-04-01T00:00:00"
                            }
                        }],
                        "assets": [{
                            "assetFiles": [{
                                "url": "https://www.lego.com/cdn/product-assets/product.img.pri/21326_Prod.jpg",
                                "fileName": "21326_Prod.jpg",
                                "type": "product.img.pri"
                            }]
                        }],
                        "availability": {
                            "marketingExitDate": "2023-12-31T00:00:00",
                            "launchDate": "2021-04-01T00:00:00"
                        },
                        "locale": {
                            "zh-cn": {
                                "display_title": "小熊维尼",
                                "title": "小熊维尼",
                                "description": "进入百亩森林去探险，拜访小熊维尼位于大橡树下的房屋。发现小熊维尼正坐在火堆旁的木头上思考。认识小猪、跳跳虎、瑞比和屹耳，帮助小熊维尼\"飘\"到树顶上收集蜂巢中的蜂蜜。参观房屋内部，发现许多逼真的物品，如一盒 Poohstick、Pooh-Coo 钟等！",
                                "comm_description": "这款乐高®创意 LEGO® Ideas 套装 (21326) 包含迪士尼小熊维尼及其位于百亩森林中大橡树下的房屋，可以抽出些时间玩乐这款套装，唤起欢乐有趣的童年记忆。",
                                "bullet_points": "• 这款乐高®创意 LEGO® Ideas 展示套装(21326) 包含迪士尼小熊维尼及其位于百木森林中的房屋，可以玩乐这款套装，尽享自我时间或高质量的家庭时光。• 包含 5 个角色：小熊维尼、小猪、跳跳虎、瑞比小人仔，以及屹耳玩偶，均带有标志性配件，其中包括小熊维尼可拼搭的红色气球。",
                                "features": [
                                    "以乐高®方式向经典故事致敬",
                                    "迪士尼小熊维尼的房屋的精致模型版。使用小熊维尼、小猪、屹耳、瑞比和跳跳虎将故事场景带入现实生活。"
                                ],
                                "additional_data": {
                                    "primary_image_grownups": {
                                        "url": "https://www.lego.com/cdn/cs/catalog/assets/blt61222576e7c73afb/1/LEGO_21326_WEB_PRI_1488.jpg",
                                        "filename": "LEGO_21326_WEB_PRI_1488.jpg"
                                    },
                                    "box_image": {
                                        "url": "https://www.lego.com/cdn/cs/catalog/assets/blt4e5f5509c565d0d9/1/21326_Box1_v29_2400.png",
                                        "filename": "21326_Box1_v29_2400.png"
                                    }
                                }
                            },
                            "en-us": {
                                "characters": [
                                    "Winnie the Pooh",
                                    "Piglet",
                                    "Tigger",
                                    "Rabbit",
                                    "Eeyore"
                                ],
                                "features": [
                                    "Pay tribute to a classic story in LEGO® style",
                                    "A detailed model of Disney Winnie the Pooh's house. Bring the scene to life with Pooh, Piglet, Eeyore, Rabbit and Tigger."
                                ]
                            }
                        }
                    }
                }]
            }
        };
    }

    // 其他套装的通用模拟数据
    return {
        "_isMockData": true,
        "hits": {
            "hits": [{
                "_source": {
                    "product_number": productNumber,
                    "product_versions": [{
                        "piece_count": 1000,
                        "availability": {
                            "marketingExitDate": "2024-12-31T00:00:00",
                            "launchDate": "2023-01-01T00:00:00"
                        }
                    }],
                    "assets": [],
                    "availability": {
                        "marketingExitDate": "2024-12-31T00:00:00",
                        "launchDate": "2023-01-01T00:00:00"
                    },
                    "locale": {
                        "zh-cn": {
                            "display_title": `乐高套装 ${productNumber}`,
                            "description": "这是一个演示套装，实际数据需要通过API获取。",
                            "comm_description": "演示用套装信息。",
                            "bullet_points": "• 这是演示数据 • 请检查网络连接后重试",
                            "features": ["演示功能1", "演示功能2"]
                        }
                    }
                }
            }]
        }
    };
}
