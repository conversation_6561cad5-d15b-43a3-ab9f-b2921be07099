#!/bin/bash

# 乐高套装查询工具一键启动脚本
# LEGO Set Query Tool One-Click Startup Script

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 清屏
clear

echo -e "${BLUE}=========================================="
echo -e "🧱 乐高套装查询工具 - LEGO Set Query Tool"
echo -e "==========================================${NC}"
echo ""

# 检查Node.js是否安装
echo -e "${CYAN}🔍 检查系统环境...${NC}"
echo -e "${CYAN}🔍 Checking system environment...${NC}"
echo ""

if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ 错误: 未找到Node.js${NC}"
    echo -e "${RED}❌ Error: Node.js not found${NC}"
    echo ""
    echo -e "${YELLOW}请先安装Node.js: https://nodejs.org/${NC}"
    echo -e "${YELLOW}Please install Node.js first: https://nodejs.org/${NC}"
    echo ""
    echo -e "${YELLOW}安装方法 / Installation methods:${NC}"
    echo -e "${YELLOW}• macOS (Homebrew): brew install node${NC}"
    echo -e "${YELLOW}• Ubuntu/Debian: sudo apt install nodejs npm${NC}"
    echo -e "${YELLOW}• CentOS/RHEL: sudo yum install nodejs npm${NC}"
    echo ""
    exit 1
fi

# 显示Node.js版本
NODE_VERSION=$(node --version)
echo -e "${GREEN}✅ Node.js版本: ${NODE_VERSION}${NC}"
echo -e "${GREEN}✅ Node.js Version: ${NODE_VERSION}${NC}"
echo ""

# 检查端口是否被占用
echo -e "${CYAN}🔍 检查端口3000是否可用...${NC}"
echo -e "${CYAN}🔍 Checking if port 3000 is available...${NC}"

if lsof -Pi :3000 -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  警告: 端口3000已被占用${NC}"
    echo -e "${YELLOW}⚠️  Warning: Port 3000 is already in use${NC}"
    echo ""
    echo -e "${YELLOW}占用端口的进程 / Process using the port:${NC}"
    lsof -Pi :3000 -sTCP:LISTEN
    echo ""
    echo -e "${YELLOW}请选择操作 / Please choose an action:${NC}"
    echo -e "${YELLOW}1. 继续启动 (可能会失败) / Continue anyway (may fail)${NC}"
    echo -e "${YELLOW}2. 杀死占用进程并启动 / Kill process and start${NC}"
    echo -e "${YELLOW}3. 退出 / Exit${NC}"
    echo ""
    read -p "请输入选择 (1, 2 或 3) / Enter choice (1, 2 or 3): " choice
    
    case $choice in
        2)
            echo -e "${CYAN}🔄 正在杀死占用端口的进程...${NC}"
            echo -e "${CYAN}🔄 Killing processes using port 3000...${NC}"
            lsof -ti:3000 | xargs kill -9 2>/dev/null
            sleep 2
            echo -e "${GREEN}✅ 进程已终止${NC}"
            echo -e "${GREEN}✅ Processes killed${NC}"
            ;;
        3)
            echo -e "${BLUE}👋 已退出${NC}"
            echo -e "${BLUE}👋 Exited${NC}"
            exit 0
            ;;
        *)
            echo -e "${YELLOW}⚠️  继续启动...${NC}"
            echo -e "${YELLOW}⚠️  Continuing...${NC}"
            ;;
    esac
fi

echo ""
echo -e "${GREEN}🚀 启动代理服务器...${NC}"
echo -e "${GREEN}🚀 Starting proxy server...${NC}"
echo ""
echo -e "${PURPLE}📱 启动完成后，请在浏览器中访问: ${CYAN}http://localhost:3000${NC}"
echo -e "${PURPLE}📱 After startup, please visit: ${CYAN}http://localhost:3000${NC}"
echo ""
echo -e "${YELLOW}💡 按 Ctrl+C 停止服务器${NC}"
echo -e "${YELLOW}💡 Press Ctrl+C to stop the server${NC}"
echo ""
echo -e "${BLUE}==========================================${NC}"
echo ""

# 设置信号处理
trap 'echo -e "\n${YELLOW}🛑 正在停止服务器...${NC}"; echo -e "${YELLOW}🛑 Stopping server...${NC}"; exit 0' INT

# 启动Node.js服务器
node proxy-server.js

# 如果服务器意外退出
echo ""
echo -e "${RED}⚠️  服务器已停止${NC}"
echo -e "${RED}⚠️  Server stopped${NC}"
echo ""
