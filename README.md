# 乐高套装信息查询工具

这是一个简易的本地网页应用，可以通过输入乐高套装编号来查询套装的详细信息，并下载相关图片。

## 功能特点

- 🔍 **套装查询**: 输入套装编号即可查询详细信息
- 🌏 **多语言支持**: 优先显示中文信息，如果没有则显示英文
- 📊 **标准化展示**: 按照统一格式展示套装信息
- 🖼️ **图片展示**: 显示所有相关图片
- 📥 **图片下载**: 一键下载所有图片到本地
- 📱 **响应式设计**: 适配不同屏幕尺寸

## 使用方法

### 方法一：使用Node.js代理服务器（推荐）

1. **安装Node.js**（如果尚未安装）
   - 访问 [nodejs.org](https://nodejs.org/) 下载并安装

2. **启动代理服务器**
   ```bash
   node proxy-server.js
   # 或者使用 npm
   npm start
   ```

3. **访问应用**
   - 在浏览器中访问：`http://localhost:3000`
   - 代理服务器会自动处理CORS问题并提供静态文件服务

### 方法二：使用Python简单服务器

1. **启动Python服务器**
   ```bash
   python3 -m http.server 8000
   ```

2. **访问应用**
   - 在浏览器中访问：`http://localhost:8000`
   - 注意：可能会遇到CORS问题，应用会自动尝试使用代理或模拟数据

### 2. 查询套装信息

1. 在输入框中输入乐高套装编号（例如：21326）
2. 点击"查询"按钮或按回车键
3. 等待查询结果显示

### 3. 查看信息

查询成功后，页面会显示以下信息：

- **套装名称**: 产品编号 + 显示标题
- **颗粒数量**: 套装包含的积木颗粒数
- **上市日期**: 套装发售日期
- **下架日期**: 套装停产日期
- **套装介绍**: 基础产品介绍
- **推广介绍**: 营销推广文案
- **详细介绍**: 详细的产品特点说明
- **角色介绍**: 套装包含的角色（如果有）
- **亮点介绍**: 产品亮点特性
- **套装图片**: 所有相关图片展示

### 4. 下载图片

点击"📥 下载所有图片"按钮可以将所有图片下载到本地。图片会自动保存到浏览器的默认下载文件夹中。

## 技术实现

### 文件结构

```
LEGO APP/
├── index.html          # 主页面
├── style.css           # 样式文件
├── script.js           # JavaScript逻辑
├── images/             # 图片存储文件夹
└── README.md           # 说明文档
```

### API接口

- **接口地址**: `https://services.slingshot.lego.com/api/v4/lego_historic_product_read/_search`
- **API密钥**: `p0OKLXd8US1YsquudM1Ov9Ja7H91jhamak9EMrRB`
- **请求方式**: POST
- **数据格式**: JSON

### 数据处理逻辑

1. **多语言处理**: 优先从`zh-cn`获取信息，如果没有则从`en-us`获取
2. **图片收集**: 从`assets`和`locale.additional_data`中收集所有图片URL
3. **日期格式化**: 将ISO日期格式转换为中文日期显示
4. **错误处理**: 完善的错误提示和重试机制

## 示例查询

可以尝试查询以下套装编号：

- `21326` - 小熊维尼
- `10294` - 泰坦尼克号
- `75192` - 千年隼
- `42143` - 法拉利 Daytona SP3

## 注意事项

1. **网络连接**: 需要稳定的网络连接来访问LEGO API
2. **CORS问题**: 如果遇到跨域问题，可能需要使用代理或在服务器环境中运行
3. **图片下载**: 浏览器可能会阻止批量下载，请允许多个文件下载
4. **API限制**: 请合理使用API，避免频繁请求

## 故障排除

### 查询失败
- 检查网络连接
- 确认套装编号是否正确
- 检查API密钥是否有效

### 图片无法显示
- 检查图片URL是否有效
- 确认网络连接稳定

### 下载失败
- 检查浏览器下载设置
- 允许多个文件下载
- 确认有足够的存储空间

## 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 基本查询功能
- 图片展示和下载
- 响应式设计

---

© 2024 乐高套装信息查询工具
