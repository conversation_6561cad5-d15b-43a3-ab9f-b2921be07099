@echo off
chcp 65001 >nul
title 乐高套装查询工具 - LEGO Set Query Tool

echo.
echo ==========================================
echo 🧱 乐高套装查询工具 - LEGO Set Query Tool
echo ==========================================
echo.

:: 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Node.js
    echo ❌ Error: Node.js not found
    echo.
    echo 请先安装Node.js: https://nodejs.org/
    echo Please install Node.js first: https://nodejs.org/
    echo.
    pause
    exit /b 1
)

:: 显示Node.js版本
for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js版本: %NODE_VERSION%
echo ✅ Node.js Version: %NODE_VERSION%
echo.

:: 检查端口是否被占用
echo 🔍 检查端口3000是否可用...
echo 🔍 Checking if port 3000 is available...
netstat -an | find "3000" | find "LISTENING" >nul
if %errorlevel% equ 0 (
    echo ⚠️  警告: 端口3000已被占用
    echo ⚠️  Warning: Port 3000 is already in use
    echo.
    echo 请选择操作 / Please choose an action:
    echo 1. 继续启动 (可能会失败) / Continue anyway (may fail)
    echo 2. 退出 / Exit
    echo.
    set /p choice="请输入选择 (1 或 2) / Enter choice (1 or 2): "
    if "%choice%"=="2" (
        echo 👋 已退出 / Exited
        pause
        exit /b 0
    )
)

echo.
echo 🚀 启动代理服务器...
echo 🚀 Starting proxy server...
echo.
echo 📱 启动完成后，请在浏览器中访问: http://localhost:3000
echo 📱 After startup, please visit: http://localhost:3000
echo.
echo 💡 按 Ctrl+C 停止服务器
echo 💡 Press Ctrl+C to stop the server
echo.
echo ==========================================
echo.

:: 启动Node.js服务器
node proxy-server.js

:: 如果服务器意外退出
echo.
echo ⚠️  服务器已停止
echo ⚠️  Server stopped
echo.
pause
