* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.usage-tips {
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 10px;
    margin-top: 15px;
    backdrop-filter: blur(10px);
}

.usage-tips p {
    margin: 5px 0;
    font-size: 0.95rem;
}

.search-section {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    margin-bottom: 30px;
}

.search-box {
    display: flex;
    gap: 15px;
    max-width: 500px;
    margin: 0 auto;
}

#productNumber {
    flex: 1;
    padding: 15px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s;
}

#productNumber:focus {
    outline: none;
    border-color: #667eea;
}

#searchBtn {
    padding: 15px 30px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    cursor: pointer;
    transition: background 0.3s;
}

#searchBtn:hover {
    background: #5a6fd8;
}

.loading {
    text-align: center;
    margin-top: 20px;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.result-section {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    overflow: hidden;
}

.product-info {
    padding: 30px;
}

.product-header {
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 20px;
    margin-bottom: 30px;
}

#productTitle {
    font-size: 2rem;
    color: #333;
    margin-bottom: 10px;
}

.product-meta {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.product-number, .piece-count {
    background: #667eea;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: bold;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.info-item {
    margin-bottom: 25px;
}

.info-item h3 {
    color: #667eea;
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.info-item p {
    color: #666;
    line-height: 1.6;
}

.description-section {
    border-top: 2px solid #f0f0f0;
    padding-top: 30px;
}

.images-section {
    border-top: 2px solid #f0f0f0;
    padding-top: 30px;
    margin-top: 30px;
}

.images-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.image-item {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 150px;
}

.image-item:hover {
    transform: translateY(-5px);
}

.image-item img {
    width: 100%;
    height: auto;
    min-height: 150px;
    max-height: 300px;
    object-fit: contain;
    background: white;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.image-item img:hover {
    transform: scale(1.02);
}

.download-section {
    text-align: center;
    margin-top: 30px;
}

.download-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 8px;
    font-size: 1rem;
    cursor: pointer;
    transition: background 0.3s;
}

.download-btn:hover {
    background: #218838;
}

#downloadStatus {
    margin-top: 15px;
    font-weight: bold;
}

.error-section {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    text-align: center;
}

.error-message h3 {
    color: #dc3545;
    margin-bottom: 15px;
}

.error-message p {
    color: #666;
    margin-bottom: 20px;
}

#retryBtn {
    background: #667eea;
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 8px;
    cursor: pointer;
    transition: background 0.3s;
}

#retryBtn:hover {
    background: #5a6fd8;
}

footer {
    text-align: center;
    margin-top: 30px;
    color: white;
    opacity: 0.8;
}

/* 多套装搜索样式 */
.multi-result-container {
    padding: 0;
}

.multi-result-header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #f0f0f0;
}

.multi-result-header h2 {
    color: #667eea;
    margin-bottom: 15px;
}

.result-stats {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.result-stats span {
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: bold;
    font-size: 0.9rem;
}

.success-count {
    background: #d4edda;
    color: #155724;
}

.error-count {
    background: #f8d7da;
    color: #721c24;
}

.total-count {
    background: #e2e3e5;
    color: #495057;
}

.success-results, .error-results {
    margin: 30px 0;
}

.success-results h3, .error-results h3 {
    color: #667eea;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.product-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
    border: 1px solid #e9ecef;
}

.product-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.card-header {
    padding: 15px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-title {
    margin: 0;
    color: #333;
    font-size: 1.1rem;
    font-weight: bold;
}

.card-number {
    background: #667eea;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: bold;
}

.card-image {
    height: 200px;
    overflow: hidden;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid #e9ecef;
}

.card-image img {
    width: 100%;
    height: auto;
    max-height: 100%;
    object-fit: contain;
    transition: transform 0.3s;
    cursor: pointer;
}

.card-image:hover img {
    transform: scale(1.02);
}

.card-info {
    padding: 15px;
}

.info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.info-label {
    color: #666;
    font-size: 0.9rem;
}

.info-value {
    color: #333;
    font-weight: bold;
    font-size: 0.9rem;
}

.card-description {
    margin-top: 12px;
    color: #666;
    font-size: 0.9rem;
    line-height: 1.4;
    border-top: 1px solid #e9ecef;
    padding-top: 12px;
}

.card-actions {
    padding: 15px;
    background: #f8f9fa;
    display: flex;
    gap: 10px;
}

.btn-view-details, .btn-view-images {
    flex: 1;
    padding: 8px 12px;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: background 0.3s;
}

.btn-view-details {
    background: #667eea;
    color: white;
}

.btn-view-details:hover {
    background: #5a6fd8;
}

.btn-view-images {
    background: #28a745;
    color: white;
}

.btn-view-images:hover {
    background: #218838;
}

.error-list {
    display: grid;
    gap: 10px;
}

.error-item {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 8px;
    color: #721c24;
}

.error-number {
    font-weight: bold;
    margin-right: 15px;
    background: #dc3545;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
}

.error-message {
    flex: 1;
}

/* 进度条样式 */
.progress-bar {
    width: 100%;
    height: 6px;
    background: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
    margin-top: 10px;
}

.progress-fill {
    height: 100%;
    background: #667eea;
    transition: width 0.3s ease;
    border-radius: 3px;
}

/* 返回按钮样式 */
.back-to-list {
    margin-bottom: 20px;
    text-align: left;
}

.back-button {
    display: inline-flex;
    align-items: center;
    padding: 10px 20px;
    background: #6c757d;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    box-shadow: 0 2px 8px rgba(108, 117, 125, 0.2);
}

.back-button:hover {
    background: #5a6268;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

.back-button:active {
    transform: translateY(0);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    header h1 {
        font-size: 2rem;
    }

    .search-box {
        flex-direction: column;
    }

    .product-meta {
        justify-content: center;
    }

    .images-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 15px;
    }

    .results-grid {
        grid-template-columns: 1fr;
    }

    .result-stats {
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }

    .card-actions {
        flex-direction: column;
    }
}

/* 列表样式 */
.list-item {
    background: #f8f9fa;
    padding: 10px 15px;
    margin: 5px 0;
    border-radius: 5px;
    border-left: 4px solid #667eea;
}

.character-tag, .feature-tag {
    display: inline-block;
    background: #e9ecef;
    color: #495057;
    padding: 5px 10px;
    margin: 3px;
    border-radius: 15px;
    font-size: 0.9rem;
}
