/* CSS变量定义 */
:root {
    /* 明亮主题 */
    --bg-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --bg-secondary: rgba(255, 255, 255, 0.95);
    --bg-card: #ffffff;
    --text-primary: #333333;
    --text-secondary: #666666;
    --text-muted: #999999;
    --border-color: #e9ecef;
    --shadow-light: 0 2px 8px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 12px rgba(0,0,0,0.15);
    --shadow-heavy: 0 8px 24px rgba(0,0,0,0.2);
    --accent-color: #667eea;
    --success-color: #28a745;
    --error-color: #dc3545;
    --warning-color: #ffc107;
    --skeleton-bg: #f0f0f0;
    --skeleton-shimmer: #e0e0e0;
}

/* 深色主题 */
[data-theme="dark"] {
    --bg-primary: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    --bg-secondary: rgba(45, 45, 45, 0.95);
    --bg-card: #2d2d2d;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-muted: #999999;
    --border-color: #404040;
    --shadow-light: 0 2px 8px rgba(0,0,0,0.3);
    --shadow-medium: 0 4px 12px rgba(0,0,0,0.4);
    --shadow-heavy: 0 8px 24px rgba(0,0,0,0.5);
    --accent-color: #4a90e2;
    --success-color: #28a745;
    --error-color: #dc3545;
    --warning-color: #ffc107;
    --skeleton-bg: #404040;
    --skeleton-shimmer: #505050;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-primary);
    min-height: 100vh;
    transition: background 0.3s ease, color 0.3s ease;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    margin-bottom: 30px;
    color: white;
}

.header-top {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
}

.header-content {
    text-align: center;
    flex: 1;
}

.header-content h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header-content p {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* 主题切换按钮 */
.theme-toggle {
    position: relative;
}

.theme-toggle-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50px;
    padding: 12px 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1.2rem;
    min-width: 60px;
    justify-content: center;
}

.theme-toggle-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: scale(1.05);
}

.theme-icon {
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.theme-icon.dark-icon {
    opacity: 0;
    position: absolute;
}

[data-theme="dark"] .theme-icon.light-icon {
    opacity: 0;
}

[data-theme="dark"] .theme-icon.dark-icon {
    opacity: 1;
    position: static;
}

.usage-tips {
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 10px;
    margin-top: 15px;
    backdrop-filter: blur(10px);
}

.usage-tips p {
    margin: 5px 0;
    font-size: 0.95rem;
}

.search-section {
    background: var(--bg-card);
    padding: 30px;
    border-radius: 15px;
    box-shadow: var(--shadow-heavy);
    margin-bottom: 30px;
    transition: background 0.3s ease, box-shadow 0.3s ease;
}

.search-box {
    display: flex;
    gap: 15px;
    max-width: 500px;
    margin: 0 auto;
}

#productNumber {
    flex: 1;
    padding: 15px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s;
}

#productNumber:focus {
    outline: none;
    border-color: #667eea;
}

#searchBtn {
    padding: 15px 30px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    cursor: pointer;
    transition: background 0.3s;
}

#searchBtn:hover {
    background: #5a6fd8;
}

/* 搜索历史记录样式 */
.search-history {
    margin-top: 20px;
    background: var(--bg-secondary);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    overflow: hidden;
    backdrop-filter: blur(10px);
    animation: slideDown 0.3s ease;
}

.search-history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: rgba(102, 126, 234, 0.1);
    border-bottom: 1px solid var(--border-color);
    color: var(--text-primary);
    font-weight: 600;
    font-size: 0.9rem;
}

.clear-history-btn {
    background: transparent;
    border: 1px solid var(--accent-color);
    color: var(--accent-color);
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.clear-history-btn:hover {
    background: var(--accent-color);
    color: white;
}

.search-history-items {
    max-height: 200px;
    overflow-y: auto;
}

.search-history-item {
    display: flex;
    align-items: center;
    padding: 10px 16px;
    cursor: pointer;
    transition: background 0.2s ease;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-primary);
}

.search-history-item:last-child {
    border-bottom: none;
}

.search-history-item:hover {
    background: rgba(102, 126, 234, 0.1);
}

.search-history-item .history-query {
    flex: 1;
    font-size: 0.9rem;
}

.search-history-item .history-time {
    font-size: 0.8rem;
    color: var(--text-muted);
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 加载动画样式 */
.loading {
    margin-top: 20px;
    /* 确保加载容器不受外部动画影响 */
    animation: none !important;
    transform: none !important;
}

.loading-content {
    display: flex;
    gap: 30px;
    align-items: flex-start;
    /* 确保内容容器不旋转 */
    animation: none !important;
    transform: none !important;
}

.loading-skeleton {
    flex: 2;
    background: var(--bg-card);
    border-radius: 12px;
    padding: 20px;
    box-shadow: var(--shadow-light);
}

.skeleton-header {
    height: 32px;
    background: var(--skeleton-bg);
    border-radius: 8px;
    margin-bottom: 16px;
    animation: shimmer 1.5s ease-in-out infinite;
}

.skeleton-lines {
    margin-bottom: 20px;
}

.skeleton-line {
    height: 16px;
    background: var(--skeleton-bg);
    border-radius: 4px;
    margin-bottom: 8px;
    animation: shimmer 1.5s ease-in-out infinite;
}

.skeleton-line.short {
    width: 60%;
}

.skeleton-images {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
}

.skeleton-image {
    height: 120px;
    background: var(--skeleton-bg);
    border-radius: 8px;
    animation: shimmer 1.5s ease-in-out infinite;
}

.loading-spinner {
    flex: 1;
    text-align: center;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--skeleton-bg);
    border-top: 4px solid var(--accent-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
    /* 确保只有spinner旋转 */
    transform-origin: center;
}

.loading-spinner p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin: 0;
    /* 明确禁止文字旋转 */
    animation: none !important;
    transform: none !important;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

.skeleton-header,
.skeleton-line,
.skeleton-image {
    background: linear-gradient(90deg, var(--skeleton-bg) 25%, var(--skeleton-shimmer) 50%, var(--skeleton-bg) 75%);
    background-size: 200px 100%;
    background-repeat: no-repeat;
}

.result-section {
    background: var(--bg-card);
    border-radius: 15px;
    box-shadow: var(--shadow-heavy);
    overflow: hidden;
    transition: background 0.3s ease, box-shadow 0.3s ease;
}

.product-info {
    padding: 30px;
}

.product-header {
    border-bottom: 2px solid var(--border-color);
    padding-bottom: 20px;
    margin-bottom: 30px;
}

#productTitle {
    font-size: 2rem;
    color: var(--text-primary);
    margin-bottom: 10px;
}

.product-meta {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.product-number, .piece-count {
    background: #667eea;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: bold;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.info-item {
    margin-bottom: 25px;
}

.info-item h3 {
    color: #667eea;
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.info-item p {
    color: #666;
    line-height: 1.6;
}

.description-section {
    border-top: 2px solid #f0f0f0;
    padding-top: 30px;
}

.images-section {
    border-top: 2px solid #f0f0f0;
    padding-top: 30px;
    margin-top: 30px;
}

.images-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.image-item {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 150px;
}

.image-item:hover {
    transform: translateY(-5px);
}

.image-item img {
    width: 100%;
    height: auto;
    min-height: 150px;
    max-height: 300px;
    object-fit: contain;
    background: white;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.image-item img:hover {
    transform: scale(1.02);
}

.download-section {
    text-align: center;
    margin-top: 30px;
}

.download-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 8px;
    font-size: 1rem;
    cursor: pointer;
    transition: background 0.3s;
}

.download-btn:hover {
    background: #218838;
}

#downloadStatus {
    margin-top: 15px;
    font-weight: bold;
}

.error-section {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    text-align: center;
}

.error-message h3 {
    color: #dc3545;
    margin-bottom: 15px;
}

.error-message p {
    color: #666;
    margin-bottom: 20px;
}

#retryBtn {
    background: #667eea;
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 8px;
    cursor: pointer;
    transition: background 0.3s;
}

#retryBtn:hover {
    background: #5a6fd8;
}

footer {
    text-align: center;
    margin-top: 30px;
    color: white;
    opacity: 0.8;
}

/* 多套装搜索样式 */
.multi-result-container {
    padding: 0;
}

.multi-result-header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #f0f0f0;
}

.multi-result-header h2 {
    color: #667eea;
    margin-bottom: 15px;
}

.result-stats {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.result-stats span {
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: bold;
    font-size: 0.9rem;
}

.success-count {
    background: #d4edda;
    color: #155724;
}

.error-count {
    background: #f8d7da;
    color: #721c24;
}

.total-count {
    background: #e2e3e5;
    color: #495057;
}

.success-results, .error-results {
    margin: 30px 0;
}

.success-results h3, .error-results h3 {
    color: #667eea;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.product-card {
    background: var(--bg-card);
    border-radius: 12px;
    box-shadow: var(--shadow-medium);
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s, background 0.3s ease;
    border: 1px solid var(--border-color);
}

.product-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-heavy);
}

.card-header {
    padding: 15px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-title {
    margin: 0;
    color: #333;
    font-size: 1.1rem;
    font-weight: bold;
}

.card-number {
    background: #667eea;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: bold;
}

.card-image {
    height: 200px;
    overflow: hidden;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid #e9ecef;
}

.card-image img {
    width: 100%;
    height: auto;
    max-height: 100%;
    object-fit: contain;
    transition: transform 0.3s;
    cursor: pointer;
}

.card-image:hover img {
    transform: scale(1.02);
}

.card-info {
    padding: 15px;
}

.info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.info-label {
    color: #666;
    font-size: 0.9rem;
}

.info-value {
    color: #333;
    font-weight: bold;
    font-size: 0.9rem;
}

.card-description {
    margin-top: 12px;
    color: #666;
    font-size: 0.9rem;
    line-height: 1.4;
    border-top: 1px solid #e9ecef;
    padding-top: 12px;
}

.card-actions {
    padding: 15px;
    background: #f8f9fa;
    display: flex;
    gap: 10px;
}

.btn-view-details, .btn-view-images {
    flex: 1;
    padding: 8px 12px;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: background 0.3s;
}

.btn-view-details {
    background: #667eea;
    color: white;
}

.btn-view-details:hover {
    background: #5a6fd8;
}

.btn-view-images {
    background: #28a745;
    color: white;
}

.btn-view-images:hover {
    background: #218838;
}

.error-list {
    display: grid;
    gap: 10px;
}

.error-item {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 8px;
    color: #721c24;
}

.error-number {
    font-weight: bold;
    margin-right: 15px;
    background: #dc3545;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
}

.error-message {
    flex: 1;
}

/* 进度条样式 */
.progress-bar {
    width: 100%;
    height: 6px;
    background: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
    margin-top: 10px;
}

.progress-fill {
    height: 100%;
    background: #667eea;
    transition: width 0.3s ease;
    border-radius: 3px;
}

/* 返回按钮样式 */
.back-to-list {
    margin-bottom: 20px;
    text-align: left;
}

.back-button {
    display: inline-flex;
    align-items: center;
    padding: 10px 20px;
    background: #6c757d;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    box-shadow: 0 2px 8px rgba(108, 117, 125, 0.2);
}

.back-button:hover {
    background: #5a6268;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

.back-button:active {
    transform: translateY(0);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .header-top {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .header-content h1 {
        font-size: 2rem;
    }

    .theme-toggle {
        align-self: center;
    }

    .search-section, .result-section, .error-section {
        padding: 20px;
    }

    .search-box {
        flex-direction: column;
        gap: 10px;
    }

    .loading-content {
        flex-direction: column;
        gap: 20px;
    }

    .skeleton-images {
        grid-template-columns: repeat(2, 1fr);
    }

    .product-meta {
        justify-content: center;
        flex-direction: column;
        gap: 10px;
    }

    .images-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 15px;
    }

    .results-grid {
        grid-template-columns: 1fr;
    }

    .result-stats {
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }

    .card-actions {
        flex-direction: column;
    }

    .search-history-items {
        max-height: 150px;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }
}

/* 列表样式 */
.list-item {
    background: #f8f9fa;
    padding: 10px 15px;
    margin: 5px 0;
    border-radius: 5px;
    border-left: 4px solid #667eea;
}

.character-tag, .feature-tag {
    display: inline-block;
    background: #e9ecef;
    color: #495057;
    padding: 5px 10px;
    margin: 3px;
    border-radius: 15px;
    font-size: 0.9rem;
}
