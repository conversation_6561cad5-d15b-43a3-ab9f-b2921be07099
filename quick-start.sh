#!/bin/bash

# 快速启动脚本 - 适合高级用户
# Quick Start Script - For Advanced Users

echo "🧱 LEGO Set Query Tool - Quick Start"
echo "🚀 Starting server..."

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js not found. Please install: https://nodejs.org/"
    exit 1
fi

# 检查端口并自动处理
if lsof -Pi :3000 -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo "⚠️  Port 3000 in use. Killing processes..."
    lsof -ti:3000 | xargs kill -9 2>/dev/null
    sleep 1
fi

# 启动服务器
echo "📱 Visit: http://localhost:3000"
echo "💡 Press Ctrl+C to stop"
echo ""

node proxy-server.js
