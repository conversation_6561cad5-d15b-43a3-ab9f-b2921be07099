// 简单的CORS代理服务器
// 使用方法: node proxy-server.js
// 然后在浏览器中访问 http://localhost:3000

const http = require('http');
const https = require('https');
const url = require('url');

const PORT = 3000;
const LEGO_API_URL = 'https://services.slingshot.lego.com/api/v4/lego_historic_product_read/_search';
const API_KEY = 'p0OKLXd8US1YsquudM1Ov9Ja7H91jhamak9EMrRB';

// 创建HTTP服务器
const server = http.createServer((req, res) => {
    // 设置CORS头
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');

    // 处理预检请求
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }

    // 解析请求URL
    const parsedUrl = url.parse(req.url, true);
    
    // 处理静态文件请求
    if (req.method === 'GET' && parsedUrl.pathname !== '/api/search') {
        serveStaticFile(req, res, parsedUrl.pathname);
        return;
    }

    // 处理API代理请求
    if (req.method === 'POST' && parsedUrl.pathname === '/api/search') {
        handleApiProxy(req, res);
        return;
    }

    // 404 处理
    res.writeHead(404, { 'Content-Type': 'text/plain' });
    res.end('Not Found');
});

// 处理API代理
function handleApiProxy(req, res) {
    console.log('🔗 收到API代理请求');
    let body = '';

    req.on('data', chunk => {
        body += chunk.toString();
    });

    req.on('end', () => {
        try {
            const requestData = JSON.parse(body);
            console.log('📋 请求数据:', JSON.stringify(requestData, null, 2));

            // 准备发送到LEGO API的数据
            const postData = JSON.stringify(requestData);

            // 配置HTTPS请求
            const options = {
                hostname: 'services.slingshot.lego.com',
                port: 443,
                path: '/api/v4/lego_historic_product_read/_search',
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-API-Key': API_KEY,
                    'Content-Length': Buffer.byteLength(postData)
                }
            };

            console.log('🚀 发送请求到LEGO API...');

            // 发送请求到LEGO API
            const apiReq = https.request(options, (apiRes) => {
                console.log(`📡 LEGO API响应状态: ${apiRes.statusCode}`);
                console.log('📡 响应头:', apiRes.headers);

                let responseData = '';

                apiRes.on('data', chunk => {
                    responseData += chunk;
                });

                apiRes.on('end', () => {
                    console.log(`✅ LEGO API响应完成，数据长度: ${responseData.length}`);

                    // 显示响应内容（特别是错误情况）
                    if (apiRes.statusCode !== 200) {
                        console.log('❌ API错误响应:', responseData);
                    }

                    // 尝试解析响应数据
                    try {
                        const parsedData = JSON.parse(responseData);
                        if (apiRes.statusCode === 200) {
                            console.log('📊 API响应摘要:', {
                                took: parsedData.took,
                                total: parsedData.hits && parsedData.hits.total && parsedData.hits.total.value,
                                hits: parsedData.hits && parsedData.hits.hits && parsedData.hits.hits.length
                            });
                        } else {
                            console.log('📊 错误详情:', parsedData);
                        }
                    } catch (parseError) {
                        console.log('⚠️ 无法解析API响应为JSON');
                    }

                    // 设置响应头
                    res.writeHead(apiRes.statusCode, {
                        'Content-Type': 'application/json',
                        'Access-Control-Allow-Origin': '*',
                        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
                        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With'
                    });
                    res.end(responseData);
                });
            });

            apiReq.on('error', (error) => {
                console.error('❌ API请求错误:', error);
                res.writeHead(500, {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                });
                res.end(JSON.stringify({
                    error: 'API请求失败',
                    details: error.message
                }));
            });

            // 发送数据
            apiReq.write(postData);
            apiReq.end();

        } catch (error) {
            console.error('请求解析错误:', error);
            res.writeHead(400, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: '请求格式错误' }));
        }
    });
}

// 服务静态文件
function serveStaticFile(req, res, pathname) {
    const fs = require('fs');
    const path = require('path');
    
    // 默认文件
    if (pathname === '/') {
        pathname = '/index.html';
    }
    
    const filePath = path.join(__dirname, pathname);
    
    // 检查文件是否存在
    fs.access(filePath, fs.constants.F_OK, (err) => {
        if (err) {
            res.writeHead(404, { 'Content-Type': 'text/plain' });
            res.end('File not found');
            return;
        }
        
        // 确定内容类型
        const ext = path.extname(filePath);
        let contentType = 'text/plain';
        
        switch (ext) {
            case '.html':
                contentType = 'text/html';
                break;
            case '.css':
                contentType = 'text/css';
                break;
            case '.js':
                contentType = 'application/javascript';
                break;
            case '.json':
                contentType = 'application/json';
                break;
            case '.png':
                contentType = 'image/png';
                break;
            case '.jpg':
            case '.jpeg':
                contentType = 'image/jpeg';
                break;
        }
        
        // 读取并发送文件
        fs.readFile(filePath, (err, data) => {
            if (err) {
                res.writeHead(500, { 'Content-Type': 'text/plain' });
                res.end('Internal Server Error');
                return;
            }
            
            res.writeHead(200, { 'Content-Type': contentType });
            res.end(data);
        });
    });
}

// 启动服务器
server.listen(PORT, () => {
    console.log(`🚀 代理服务器已启动`);
    console.log(`📱 本地访问: http://localhost:${PORT}`);
    console.log(`🔗 API代理: http://localhost:${PORT}/api/search`);
    console.log(`📁 静态文件: 当前目录`);
    console.log(`\n使用说明:`);
    console.log(`1. 在浏览器中访问 http://localhost:${PORT}`);
    console.log(`2. 输入乐高套装编号进行查询`);
    console.log(`3. 如果直接API调用失败，会自动使用代理`);
    console.log(`\n按 Ctrl+C 停止服务器`);
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n🛑 正在关闭服务器...');
    server.close(() => {
        console.log('✅ 服务器已关闭');
        process.exit(0);
    });
});
