# 🚀 一键启动脚本使用说明

## 📋 启动脚本介绍

为了让你更方便地启动乐高套装查询工具，我们提供了针对不同操作系统的一键启动脚本。

## 💻 支持的操作系统

### 🪟 Windows 系统
**文件**: `start.bat`
**使用方法**: 双击 `start.bat` 文件

### 🍎 macOS 系统
**文件**: `start.sh`
**使用方法**: 
1. 打开终端 (Terminal)
2. 拖拽 `start.sh` 文件到终端窗口
3. 按回车键执行

### 🐧 Linux 系统
**文件**: `start.sh`
**使用方法**:
```bash
# 方法1: 直接执行
./start.sh

# 方法2: 使用bash执行
bash start.sh
```

## ✨ 脚本功能特性

### 🔍 智能检测
- ✅ **Node.js 检测** - 自动检查是否安装Node.js
- ✅ **版本显示** - 显示当前Node.js版本
- ✅ **端口检测** - 检查3000端口是否被占用
- ✅ **进程管理** - 可选择杀死占用端口的进程

### 🎨 用户友好
- ✅ **彩色输出** - 使用颜色区分不同类型的信息
- ✅ **双语支持** - 中英文双语提示
- ✅ **清晰指引** - 详细的操作说明和错误提示
- ✅ **优雅退出** - Ctrl+C 优雅停止服务器

### 🛠️ 错误处理
- ✅ **依赖检查** - 检查必需的软件是否安装
- ✅ **端口冲突** - 智能处理端口占用问题
- ✅ **错误提示** - 清晰的错误信息和解决建议

## 📱 启动后的操作

### 1. 启动成功
当你看到以下信息时，说明启动成功：
```
🚀 代理服务器已启动
📱 本地访问: http://localhost:3000
```

### 2. 访问应用
在浏览器中访问：
```
http://localhost:3000
```

### 3. 停止服务器
按 `Ctrl + C` 停止服务器

## 🔧 故障排除

### 问题1: 提示"未找到Node.js"
**解决方案**:
1. 访问 [https://nodejs.org/](https://nodejs.org/) 下载安装Node.js
2. 安装完成后重新运行启动脚本

### 问题2: 端口3000被占用
**解决方案**:
- **Windows**: 脚本会提示选择继续或退出
- **macOS/Linux**: 脚本会提供3个选项：
  1. 继续启动 (可能失败)
  2. 自动杀死占用进程
  3. 退出

### 问题3: 权限问题 (macOS/Linux)
**解决方案**:
```bash
# 添加执行权限
chmod +x start.sh

# 然后重新执行
./start.sh
```

### 问题4: 脚本无法执行 (macOS)
**解决方案**:
1. 右键点击 `start.sh`
2. 选择"打开方式" → "终端"
3. 或在终端中输入: `bash start.sh`

## 🎯 使用技巧

### 快速启动
1. **Windows**: 创建桌面快捷方式到 `start.bat`
2. **macOS**: 将 `start.sh` 添加到 Dock 或创建别名
3. **Linux**: 创建桌面快捷方式或添加到应用菜单

### 自动打开浏览器 (可选)
如果你希望启动后自动打开浏览器，可以在脚本启动后手动访问：
```
http://localhost:3000
```

## 📊 脚本输出说明

### 🟢 绿色信息
- 成功信息
- 版本信息
- 启动信息

### 🟡 黄色信息
- 警告信息
- 操作提示
- 使用说明

### 🔵 蓝色信息
- 标题信息
- 分隔线
- 一般信息

### 🔴 红色信息
- 错误信息
- 失败提示

## 🎉 启动成功后

当脚本成功启动后，你将看到：

1. **服务器状态** - 代理服务器运行状态
2. **访问地址** - http://localhost:3000
3. **API地址** - http://localhost:3000/api/search
4. **使用说明** - 基本操作指引

## 💡 小贴士

1. **首次使用**: 建议先阅读 `README.md` 了解应用功能
2. **网络问题**: 如遇网络问题，检查防火墙设置
3. **性能优化**: 关闭不必要的程序以获得更好性能
4. **数据安全**: 应用不会保存任何个人数据

## 🆘 获取帮助

如果遇到问题：
1. 查看 `HOW_TO_START.md` 获取详细启动说明
2. 查看 `README.md` 了解应用功能
3. 查看其他文档获取更多信息

---

**现在你可以使用一键启动脚本轻松启动乐高套装查询工具了！** 🎊
