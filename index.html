<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Finding LEGO</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <div class="header-top">
                <div class="header-content">
                    <h1>Finding LEGO</h1>
                    <p>输入套装编号查询详细信息</p>
                </div>
                <div class="theme-toggle">
                    <button id="themeToggle" class="theme-toggle-btn" title="切换主题">
                        <span class="theme-icon light-icon">☀️</span>
                        <span class="theme-icon dark-icon">🌙</span>
                    </button>
                </div>
            </div>
            <div class="usage-tips">
                <p>💡 <strong>使用提示:</strong> 支持单个或多个套装查询</p>
                <p>📝 <strong>格式:</strong> 单个编号 (如: 21326) 或多个编号用空格分隔 (如: 21326 42056 10375)</p>
            </div>
        </header>

        <div class="search-section">
            <div class="search-box">
                <input type="text" id="productNumber" placeholder="请输入套装编号 (例如: 21326 42056 10375)" maxlength="100">
                <button id="searchBtn">查询</button>
            </div>
            <div class="search-history" id="searchHistory" style="display: none;">
                <div class="search-history-header">
                    <span>🕒 搜索历史</span>
                    <button class="clear-history-btn" id="clearHistoryBtn">清空</button>
                </div>
                <div class="search-history-items" id="searchHistoryItems">
                    <!-- 搜索历史项目将在这里动态生成 -->
                </div>
            </div>
            <div class="loading" id="loading" style="display: none;">
                <div class="loading-content">
                    <div class="loading-skeleton">
                        <div class="skeleton-header"></div>
                        <div class="skeleton-lines">
                            <div class="skeleton-line"></div>
                            <div class="skeleton-line"></div>
                            <div class="skeleton-line short"></div>
                        </div>
                        <div class="skeleton-images">
                            <div class="skeleton-image"></div>
                            <div class="skeleton-image"></div>
                            <div class="skeleton-image"></div>
                        </div>
                    </div>
                    <div class="loading-spinner">
                        <div class="spinner"></div>
                        <p>正在查询中...</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="result-section" id="resultSection" style="display: none;">
            <div class="product-info">
                <div class="product-header">
                    <h2 id="productTitle"></h2>
                    <div class="product-meta">
                        <span class="product-number" id="productNumberDisplay"></span>
                        <span class="piece-count" id="pieceCount"></span>
                    </div>
                </div>

                <div class="product-details">
                    <div class="info-grid">
                        <div class="info-item">
                            <h3>📅 上市日期</h3>
                            <p id="launchDate"></p>
                        </div>
                        <div class="info-item">
                            <h3>📅 下架日期</h3>
                            <p id="exitDate"></p>
                        </div>
                    </div>

                    <div class="description-section">
                        <div class="info-item">
                            <h3>📝 套装介绍</h3>
                            <p id="description"></p>
                        </div>
                        
                        <div class="info-item">
                            <h3>🎯 推广介绍</h3>
                            <p id="commDescription"></p>
                        </div>

                        <div class="info-item">
                            <h3>📋 详细介绍</h3>
                            <div id="bulletPoints"></div>
                        </div>

                        <div class="info-item" id="charactersSection" style="display: none;">
                            <h3>👥 角色介绍</h3>
                            <div id="characters"></div>
                        </div>

                        <div class="info-item" id="featuresSection" style="display: none;">
                            <h3>⭐ 亮点介绍</h3>
                            <div id="features"></div>
                        </div>
                    </div>
                </div>

                <div class="images-section">
                    <h3>🖼️ 套装图片</h3>
                    <div class="images-grid" id="imagesGrid">
                        <!-- 图片将在这里动态加载 -->
                    </div>
                    <div class="download-section">
                        <button id="downloadAllBtn" class="download-btn">📥 下载所有图片</button>
                        <div id="downloadStatus"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="error-section" id="errorSection" style="display: none;">
            <div class="error-message">
                <h3>❌ 查询失败</h3>
                <p id="errorMessage"></p>
                <button id="retryBtn">重试</button>
            </div>
        </div>
    </div>

    <footer>
        <p>© 2025 BricksJoy乐趣</p>
    </footer>

    <script src="script.js"></script>
</body>
</html>
