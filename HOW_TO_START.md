# 🚀 如何启动乐高套装查询工具

## 📋 启动步骤

### 1. 确保已安装 Node.js
- 访问 [https://nodejs.org/](https://nodejs.org/) 下载并安装 Node.js
- 或使用包管理器安装：
  ```bash
  # macOS (使用 Homebrew)
  brew install node
  
  # Windows (使用 Chocolatey)
  choco install nodejs
  
  # Ubuntu/Debian
  sudo apt install nodejs npm
  ```

### 2. 启动应用
在终端中执行以下命令：

```bash
# 进入项目目录
cd "LEGO APP"

# 启动代理服务器
node proxy-server.js
```

### 3. 访问应用
启动成功后，在浏览器中访问：
```
http://localhost:3000
```

## 🎯 使用说明

### 单套装查询
在搜索框中输入单个套装编号：
```
21326
```

### 多套装查询
在搜索框中输入多个套装编号，用空格分隔：
```
21326 42056 10294
```

### 功能特性
- ✅ **真实API数据** - 直接从LEGO官方API获取
- ✅ **多套装批量查询** - 支持同时查询多个套装
- ✅ **完美图片显示** - 无裁切、完整显示
- ✅ **图片查看器** - 点击图片查看大图
- ✅ **智能导航** - 在详情和列表间无缝切换
- ✅ **响应式设计** - 完美适配各种设备

## 🛠️ 故障排除

### 如果遇到端口占用问题
```bash
# 查找占用3000端口的进程
lsof -i :3000

# 杀死进程（替换PID为实际进程ID）
kill -9 PID
```

### 如果遇到网络问题
- 检查网络连接
- 确认防火墙设置
- 尝试重启代理服务器

## 📁 项目文件说明

### 核心文件
- `index.html` - 主应用界面
- `style.css` - 样式文件
- `script.js` - 功能逻辑
- `proxy-server.js` - CORS代理服务器

### 文档文件
- `README.md` - 项目介绍
- `FINAL_SUMMARY.md` - 项目总结
- `UX_OPTIMIZATION_SUMMARY.md` - 用户体验优化说明
- `IMAGE_OPTIMIZATION_SUMMARY.md` - 图片优化说明

## 🎉 享受使用！

现在你可以开始使用这个功能完整的乐高套装查询工具了！
